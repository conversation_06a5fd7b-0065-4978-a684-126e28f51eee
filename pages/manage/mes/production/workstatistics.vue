<template>
  <a-card title="工作量统计汇总">
    <!-- 搜索区域 -->
    <div class="search-area" style="margin-bottom: 16px">
      <a-row :gutter="16" align="middle">
        <a-col :span="6">
          <a-form-item label="时间范围" style="margin-bottom: 0">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              format="YYYY-MM-DD"
              :placeholder="['开始日期', '结束日期']"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="生产员" style="margin-bottom: 0">
            <manage-user-selector
              v-model:value="searchForm.selectedUser"
              :multiple="false"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
        <a-col :span="6" style="text-align: right">
          <a-space>
            <span class="search-info">
              共 <strong>{{ dataSource.length }}</strong> 名生产员
            </span>
            <a-button @click="handleExport" :loading="exportLoading">
              <template #icon>
                <DownloadOutlined />
              </template>
              导出
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards" style="margin-bottom: 16px">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总派工量"
              :value="totalStats.totalAssignedQuantity"
              :precision="0"
              suffix="件"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总产量"
              :value="totalStats.totalProductionQuantity"
              :precision="0"
              suffix="件"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="合格品数量"
              :value="totalStats.totalQualifiedQuantity"
              :precision="0"
              suffix="件"
              :value-style="{ color: '#13c2c2' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="平均完成率"
              :value="totalStats.averageCompletionRate"
              :precision="2"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      row-key="userId"
      bordered
      size="small"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'userName'">
          <a-space>
            <a-avatar size="small" :style="{ backgroundColor: getAvatarColor(record.userName) }">
              {{ record.userName.charAt(0) }}
            </a-avatar>
            <span>{{ record.userName }}</span>
          </a-space>
        </template>
        <template v-if="column.key === 'completionRate'">
          <a-progress
            :percent="parseFloat(record.completionRate)"
            size="small"
            :status="getProgressStatus(parseFloat(record.completionRate))"
          />
        </template>
        <template v-if="column.key === 'qualifiedRate'">
          <a-progress
            :percent="parseFloat(record.qualifiedRate)"
            size="small"
            :status="getQualityStatus(parseFloat(record.qualifiedRate))"
          />
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleViewDetail(record)">
              查看详情
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="生产员工作详情"
      :width="1000"
      :footer="null"
    >
      <div v-if="selectedUser">
        <a-descriptions title="基本信息" bordered :column="3" size="small">
          <a-descriptions-item label="姓名">
            {{ selectedUser.userName }}
          </a-descriptions-item>
          <a-descriptions-item label="工号">
            {{ selectedUser.userCode || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="派工任务数">
            {{ selectedUser.assignedTaskCount }}
          </a-descriptions-item>
          <a-descriptions-item label="派工总量">
            {{ selectedUser.assignedQuantity }} 件
          </a-descriptions-item>
          <a-descriptions-item label="报工次数">
            {{ selectedUser.reportCount }}
          </a-descriptions-item>
          <a-descriptions-item label="总产量">
            {{ selectedUser.totalQuantity }} 件
          </a-descriptions-item>
          <a-descriptions-item label="合格品数量">
            {{ selectedUser.qualifiedQuantity }} 件
          </a-descriptions-item>
          <a-descriptions-item label="不合格品数量">
            {{ selectedUser.unqualifiedQuantity }} 件
          </a-descriptions-item>
          <a-descriptions-item label="完成率">
            {{ selectedUser.completionRate }}%
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted } from "vue";
  import { message } from "ant-design-vue";
  import {
    SearchOutlined,
    ReloadOutlined,
    DownloadOutlined,
  } from "@ant-design/icons-vue";
  import dayjs, { type Dayjs } from "dayjs";
  import type { TableColumnProps, PaginationProps } from "ant-design-vue";

  // 页面状态
  const loading = ref(false);
  const exportLoading = ref(false);
  const dataSource = ref<any[]>([]);
  const detailModalVisible = ref(false);
  const selectedUser = ref<any>(null);

  // 搜索表单
  const searchForm = reactive({
    dateRange: [dayjs().subtract(30, 'day'), dayjs()] as [Dayjs, Dayjs],
    selectedUser: null as any,
  });

  // 分页配置
  const pagination = computed<PaginationProps>(() => ({
    current: 1,
    total: dataSource.value.length,
    pageSize: 20,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    showSizeChanger: true,
    showQuickJumper: true,
  }));

  // 表格列定义
  const columns: TableColumnProps[] = [
    {
      title: "序号",
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: "生产员",
      dataIndex: "userName",
      key: "userName",
      width: 120,
    },
    {
      title: "工号",
      dataIndex: "userCode",
      width: 100,
    },
    {
      title: "派工任务数",
      dataIndex: "assignedTaskCount",
      width: 100,
      sorter: (a, b) => a.assignedTaskCount - b.assignedTaskCount,
    },
    {
      title: "派工总量",
      dataIndex: "assignedQuantity",
      width: 100,
      sorter: (a, b) => a.assignedQuantity - b.assignedQuantity,
      customRender: ({ text }) => `${text} 件`,
    },
    {
      title: "报工次数",
      dataIndex: "reportCount",
      width: 100,
      sorter: (a, b) => a.reportCount - b.reportCount,
    },
    {
      title: "总产量",
      dataIndex: "totalQuantity",
      width: 100,
      sorter: (a, b) => a.totalQuantity - b.totalQuantity,
      customRender: ({ text }) => `${text} 件`,
    },
    {
      title: "合格品数量",
      dataIndex: "qualifiedQuantity",
      width: 110,
      sorter: (a, b) => a.qualifiedQuantity - b.qualifiedQuantity,
      customRender: ({ text }) => `${text} 件`,
    },
    {
      title: "不合格品数量",
      dataIndex: "unqualifiedQuantity",
      width: 120,
      sorter: (a, b) => a.unqualifiedQuantity - b.unqualifiedQuantity,
      customRender: ({ text }) => `${text} 件`,
    },
    {
      title: "完成率",
      dataIndex: "completionRate",
      key: "completionRate",
      width: 120,
      sorter: (a, b) => parseFloat(a.completionRate) - parseFloat(b.completionRate),
    },
    {
      title: "合格率",
      dataIndex: "qualifiedRate",
      key: "qualifiedRate",
      width: 120,
      sorter: (a, b) => parseFloat(a.qualifiedRate) - parseFloat(b.qualifiedRate),
    },
    {
      title: "操作",
      key: "action",
      width: 100,
      fixed: "right",
    },
  ];

  // 总计统计
  const totalStats = computed(() => {
    const stats = {
      totalAssignedQuantity: 0,
      totalProductionQuantity: 0,
      totalQualifiedQuantity: 0,
      averageCompletionRate: 0,
    };

    if (dataSource.value.length === 0) return stats;

    dataSource.value.forEach((item) => {
      stats.totalAssignedQuantity += item.assignedQuantity;
      stats.totalProductionQuantity += item.totalQuantity;
      stats.totalQualifiedQuantity += item.qualifiedQuantity;
    });

    // 计算平均完成率
    const totalCompletionRate = dataSource.value.reduce(
      (sum, item) => sum + parseFloat(item.completionRate),
      0
    );
    stats.averageCompletionRate = dataSource.value.length > 0
      ? totalCompletionRate / dataSource.value.length
      : 0;

    return stats;
  });

  // API接口
  const getWorkStatistics = useApiTrpc().admin.production.getWorkStatistics.query;

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const [startDate, endDate] = searchForm.dateRange;
      const params = {
        startDate: startDate?.format("YYYY-MM-DD"),
        endDate: endDate?.format("YYYY-MM-DD"),
        userId: searchForm.selectedUser?.id,
      };

      const response = await getWorkStatistics(params);

      if (response.code === 1) {
        dataSource.value = response.data || [];
      } else {
        message.error(response.message || "查询失败");
      }
    } catch (error) {
      console.error("获取工作量统计失败:", error);
      message.error("获取工作量统计失败");
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理
  const handleSearch = () => {
    fetchData();
  };

  // 重置处理
  const handleReset = () => {
    searchForm.dateRange = [dayjs().subtract(30, 'day'), dayjs()];
    searchForm.selectedUser = null;
    fetchData();
  };

  // 导出处理
  const handleExport = () => {
    exportLoading.value = true;
    try {
      // 这里可以实现导出功能
      message.success("导出功能开发中");
    } catch (error) {
      message.error("导出失败");
    } finally {
      exportLoading.value = false;
    }
  };

  // 查看详情
  const handleViewDetail = (record: any) => {
    selectedUser.value = record;
    detailModalVisible.value = true;
  };

  // 获取头像颜色
  const getAvatarColor = (name: string) => {
    const colors = [
      '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#1890ff',
      '#52c41a', '#eb2f96', '#722ed1', '#fa541c', '#13c2c2'
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // 获取进度条状态
  const getProgressStatus = (percent: number) => {
    if (percent >= 100) return 'success';
    if (percent >= 80) return 'active';
    if (percent >= 60) return 'normal';
    return 'exception';
  };

  // 获取质量状态
  const getQualityStatus = (percent: number) => {
    if (percent >= 95) return 'success';
    if (percent >= 90) return 'active';
    if (percent >= 80) return 'normal';
    return 'exception';
  };

  // 页面加载时获取数据
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  .search-area {
    background: #fafafa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
  }

  .search-info {
    color: #666;
    font-size: 14px;
  }

  .statistics-cards {
    margin-bottom: 16px;
  }

  :deep(.ant-statistic-title) {
    font-size: 14px;
    color: #666;
  }

  :deep(.ant-statistic-content) {
    font-size: 20px;
    font-weight: 600;
  }

  :deep(.ant-progress-text) {
    font-size: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 600;
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: 600;
    background: #fafafa;
  }
</style>