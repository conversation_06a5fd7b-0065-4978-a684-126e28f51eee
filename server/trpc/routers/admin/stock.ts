import {
  router,
  mergeRouters,
  singleResultProcedure,
  tableResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  salesOutRecordCreateSchema,
  salesOutRecordQuerySchema,
} from "~/schemas/sales";

export const queryPurchaseOrders = tableResultProcedure
  .meta({ permission: ["stock:purchase:query"], authRequired: true })
  .input(z.object({}))
  .query(async ({ input }) => {
    const purchaseOrders = await prisma.purchaseOrder.findManyWithCount({
      take: input.take,
      skip: input.skip,
      where: {
        status: {
          in: ["approved", "partially_received", "completed"],
        },
      },
    });
    return {
      code: 1,
      message: "success",
      data: purchaseOrders,
    };
  });
// 查询库存列表
export const queryStock = tableResultProcedure
  .meta({ permission: ["stock:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      materialCode: z.string().optional(),
      materialName: z.string().optional(),
      warehouseId: z.number().optional(),
      batchNo: z.string().optional(),
    })
  )
  .query(async ({ input }) => {
    const where = {
      materiel: {
        code: input.materialCode ? { contains: input.materialCode } : undefined,
        name: input.materialName ? { contains: input.materialName } : undefined,
      },
      warehouse_id: input.warehouseId,
      batch_no: input.batchNo ? { contains: input.batchNo } : undefined,
    };

    const stock = await prisma.stock.findManyWithCount({
      where,
      take: input.take,
      skip: input.skip,
      include: {
        materiel: true,
        warehouse: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    return {
      code: 1,
      message: "success",
      data: stock,
    };
  });

// 根据物料ID查询批号列表
export const queryMaterielBatches = singleResultProcedure
  .meta({ permission: ["stock:query"], authRequired: true })
  .input(
    z.object({
      materielId: z.number(),
      warehouseId: z.number().optional(),
    })
  )
  .query(async ({ input }) => {
    try {
      const where: any = {
        materiel_id: input.materielId,
        quantity: { gt: 0 }, // 只查询有库存的批号
      };

      // 如果指定了仓库，则过滤仓库
      if (input.warehouseId) {
        where.warehouse_id = input.warehouseId;
      }

      const stocks = await prisma.stock.findMany({
        where,
        select: {
          id: true,
          materiel_id: true,
          warehouse_id: true,
          batch_no: true,
          quantity: true,
          warehouse: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          batch_no: "asc",
        },
      });

      return {
        code: 1,
        message: "success",
        data: stocks,
      };
    } catch (error) {
      console.error("查询物料批号失败:", error);
      return {
        code: 0,
        message: "查询物料批号失败",
        data: [],
      };
    }
  });

// 查询库存列表（按物料分组）
export const queryStockGrouped = tableResultProcedure
  .meta({ permission: ["stock:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      materialCode: z.string().optional(),
      materialName: z.string().optional(),
      warehouseId: z.number().optional(),
      batchNo: z.string().optional(),
    })
  )
  .query(async ({ input }) => {
    try {
      // 构建查询条件
      const where: any = {
        warehouse_id: input.warehouseId,
        batch_no: input.batchNo ? { contains: input.batchNo } : undefined,
      };

      // 如果没有指定仓库ID，则排除废料库的库存
      // 如果指定了仓库ID，则按用户选择的仓库查询（包括废料库）
      if (!input.warehouseId) {
        where.warehouse = {
          type: {
            not: "waste"
          }
        };
      }

      // 处理物料搜索条件
      if (input.materialCode || input.materialName) {
        const materielConditions: any = {};

        // 如果物料编码和物料名称是相同的值（来自快速搜索）
        if (input.materialCode === input.materialName && input.materialCode) {
          // 使用OR条件同时搜索编码和名称
          materielConditions.OR = [
            { code: { contains: input.materialCode } },
            { name: { contains: input.materialCode } },
          ];
        } else {
          // 分别处理编码和名称搜索
          if (input.materialCode) {
            materielConditions.code = { contains: input.materialCode };
          }
          if (input.materialName && input.materialName !== input.materialCode) {
            materielConditions.name = { contains: input.materialName };
          }
        }

        where.materiel = materielConditions;
      }

      // 先获取所有符合条件的库存记录
      const allStock = await prisma.stock.findMany({
        where,
        include: {
          materiel: true,
          warehouse: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // 按物料分组
      const groupedStock = new Map();

      allStock.forEach((stock) => {
        const key = `${stock.materiel_id}`;
        if (!groupedStock.has(key)) {
          groupedStock.set(key, {
            id: stock.materiel_id,
            materiel: stock.materiel,
            totalQuantity: 0,
            batches: [],
          });
        }

        const group = groupedStock.get(key);
        // 确保数量转换为数字
        const quantity =
          typeof stock.quantity === "string"
            ? parseFloat(stock.quantity)
            : Number(stock.quantity);
        group.totalQuantity += quantity;
        group.batches.push({
          id: stock.id,
          batch_no: stock.batch_no,
          quantity: stock.quantity,
          warehouse: stock.warehouse,
          production_date: stock.production_date,
          expiry_date: stock.expiry_date,
          location: stock.location,
          status: stock.status,
          note: stock.note,
          createdAt: stock.createdAt,
          updatedAt: stock.updatedAt,
        });
      });

      // 转换为数组并分页
      const result = Array.from(groupedStock.values());
      const total = result.length;

      // 手动分页
      const skip = input.skip || 0;
      const take = input.take || 10;
      const paginatedResult = result.slice(skip, skip + take);

      return {
        code: 1,
        message: "success",
        data: {
          result: paginatedResult,
          total: total,
        },
      };
    } catch (error) {
      console.error("查询分组库存失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询库存失败",
      });
    }
  });

// 入库处理
export const stockIn = singleResultProcedure
  .meta({ permission: ["stock:in"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      warehouse_id: z.number(),
      batch_no: z.string(),
      quantity: z.number(),
      production_date: z.date().optional(),
      expiry_date: z.date().optional(),
      location: z.string().optional(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const { materiel_id, warehouse_id, batch_no, quantity, ...rest } = input;

    // 检查物料是否存在
    const materiel = await prisma.materiel.findUnique({
      where: { id: materiel_id },
    });
    if (!materiel) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "物料不存在",
      });
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: warehouse_id },
    });
    if (!warehouse) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "仓库不存在",
      });
    }

    // 事务处理：更新或创建库存记录，同时更新物料总库存
    const result = await prisma.$transaction(async (tx) => {
      // 查找现有库存记录
      const existingStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (existingStock) {
        // 更新现有库存记录
        const updatedStock = await tx.stock.update({
          where: { id: existingStock.id },
          data: {
            quantity: { increment: quantity },
            ...rest,
          },
        });

        // 更新物料总库存
        await tx.materiel.update({
          where: { id: materiel_id },
          data: {
            stock: { increment: quantity },
          },
        });

        return updatedStock;
      } else {
        // 创建新的库存记录
        const newStock = await tx.stock.create({
          data: {
            materiel_id,
            warehouse_id,
            batch_no,
            quantity,
            ...rest,
          },
        });

        // 更新物料总库存
        await tx.materiel.update({
          where: { id: materiel_id },
          data: {
            stock: { increment: quantity },
          },
        });

        return newStock;
      }
    });

    return {
      code: 1,
      message: "入库成功",
      data: result,
    };
  });

// 出库处理
export const stockOut = singleResultProcedure
  .meta({ permission: ["stock:out"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      warehouse_id: z.number(),
      batch_no: z.string(),
      quantity: z.number(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const { materiel_id, warehouse_id, batch_no, quantity, note } = input;

    // 事务处理：更新库存记录，同时更新物料总库存
    const result = await prisma.$transaction(async (tx) => {
      // 查找库存记录
      const stock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (!stock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存记录不存在",
        });
      }

      if (stock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 更新库存记录
      const updatedStock = await tx.stock.update({
        where: { id: stock.id },
        data: {
          quantity: { decrement: quantity },
          note: note,
        },
      });

      // 更新物料总库存
      await tx.materiel.update({
        where: { id: materiel_id },
        data: {
          stock: { decrement: quantity },
        },
      });

      return updatedStock;
    });

    return {
      code: 1,
      message: "出库成功",
      data: result,
    };
  });

// 库存调拨处理
export const stockTransfer = singleResultProcedure
  .meta({ permission: ["stock:transfer"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      source_warehouse_id: z.number(),
      target_warehouse_id: z.number(),
      source_batch_no: z.string(),
      target_batch_no: z.string(),
      quantity: z.number(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const {
      materiel_id,
      source_warehouse_id,
      target_warehouse_id,
      source_batch_no,
      target_batch_no,
      quantity,
      note,
    } = input;

    // 检查源仓库和目标仓库不能相同
    if (source_warehouse_id === target_warehouse_id) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "源仓库和目标仓库不能相同",
      });
    }

    // 事务处理：从源仓库出库，同时向目标仓库入库
    const result = await prisma.$transaction(async (tx) => {
      // 查找源库存记录
      const sourceStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id: source_warehouse_id,
            batch_no: source_batch_no,
          },
        },
      });

      if (!sourceStock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "源库存记录不存在",
        });
      }

      if (sourceStock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "源库存不足",
        });
      }

      // 从源仓库减少库存
      await tx.stock.update({
        where: { id: sourceStock.id },
        data: {
          quantity: { decrement: quantity },
          note: `调拨至${target_warehouse_id}仓库: ${note || ""}`,
        },
      });

      // 查找目标库存记录
      const targetStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id: target_warehouse_id,
            batch_no: target_batch_no,
          },
        },
      });

      // 向目标仓库增加库存
      if (targetStock) {
        // 更新现有库存记录
        await tx.stock.update({
          where: { id: targetStock.id },
          data: {
            quantity: { increment: quantity },
            note: `从${source_warehouse_id}仓库调拨: ${note || ""}`,
          },
        });
      } else {
        // 创建新的库存记录
        await tx.stock.create({
          data: {
            materiel_id,
            warehouse_id: target_warehouse_id,
            batch_no: target_batch_no,
            quantity,
            note: `从${source_warehouse_id}仓库调拨: ${note || ""}`,
          },
        });
      }

      // 总库存不变，因为只是在仓库间调拨
      return { success: true };
    });

    return {
      code: 1,
      message: "库存调拨成功",
      data: result,
    };
  });

// 销售出库处理
export const salesOut = singleResultProcedure
  .meta({ permission: ["stock:sales:out"], authRequired: true })
  .input(salesOutRecordCreateSchema)
  .mutation(async ({ input, ctx }) => {
    const {
      customer_id,
      materiel_id,
      warehouse_id,
      batch_no,
      quantity,
      order_no,
      note,
    } = input;

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customer_id },
    });
    if (!customer) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "客户不存在",
      });
    }

    // 检查物料是否存在
    const materiel = await prisma.materiel.findUnique({
      where: { id: materiel_id },
    });
    if (!materiel) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "物料不存在",
      });
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: warehouse_id },
    });
    if (!warehouse) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "仓库不存在",
      });
    }

    // 事务处理：更新库存记录，同时更新物料总库存，创建销售出库记录
    const result = await prisma.$transaction(async (tx) => {
      // 查找库存记录
      const stock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (!stock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存记录不存在",
        });
      }

      if (stock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 更新库存记录
      const updatedStock = await tx.stock.update({
        where: { id: stock.id },
        data: {
          quantity: { decrement: quantity },
          note: `销售出库: ${order_no}, ${note || ""}`,
        },
      });

      // 更新物料总库存
      await tx.materiel.update({
        where: { id: materiel_id },
        data: {
          stock: { decrement: quantity },
        },
      });

      // 创建销售出库记录
      const salesOutRecord = await tx.salesOutRecord.create({
        data: {
          customer_id,
          materiel_id,
          warehouse_id,
          batch_no,
          quantity,
          order_no,
          note,
          user_id: ctx.user.id,
        },
      });

      return {
        updatedStock,
        salesOutRecord,
      };
    });

    return {
      code: 1,
      message: "销售出库成功",
      data: result,
    };
  });

// 查询销售出库记录
export const querySalesOutRecords = tableResultProcedure
  .meta({ permission: ["stock:sales:query"], authRequired: true })
  .input(salesOutRecordQuerySchema)
  .query(async ({ input }) => {
    const where: any = {};

    if (input.customer_id) {
      where.customer_id = input.customer_id;
    }

    if (input.materiel_id) {
      where.materiel_id = input.materiel_id;
    }

    if (input.warehouse_id) {
      where.warehouse_id = input.warehouse_id;
    }

    if (input.order_no) {
      where.order_no = {
        contains: input.order_no,
      };
    }

    if (input.startDate && input.endDate) {
      where.createdAt = {
        gte: new Date(input.startDate),
        lte: new Date(input.endDate),
      };
    }

    const salesOutRecords = await prisma.salesOutRecord.findManyWithCount({
      where,
      take: input.take,
      skip: input.skip,
      include: {
        customer: true,
        materiel: true,
        warehouse: true,
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      code: 1,
      message: "success",
      data: salesOutRecords,
    };
  });

// 批量创建初始库存
export const createInitialStock = singleResultProcedure
  .meta({ permission: ["stock:create"], authRequired: true })
  .input(
    z.object({
      stockData: z.array(
        z.object({
          materiel_id: z.number(),
          warehouse_id: z.number(),
          batch_no: z.string(),
          quantity: z.number(),
          production_date: z.string().optional().nullable(),
          expiry_date: z.string().optional().nullable(),
          location: z.string().optional().nullable(),
          note: z.string().optional().nullable(),
        })
      ),
    })
  )
  .mutation(async ({ input }) => {
    const { stockData } = input;

    if (!stockData || stockData.length === 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "库存数据不能为空",
      });
    }

    // 验证所有物料和仓库是否存在
    const materielIds = [...new Set(stockData.map((item) => item.materiel_id))];
    const warehouseIds = [
      ...new Set(stockData.map((item) => item.warehouse_id)),
    ];

    const materiels = await prisma.materiel.findMany({
      where: { id: { in: materielIds } },
    });

    const warehouses = await prisma.warehouse.findMany({
      where: { id: { in: warehouseIds } },
    });

    if (materiels.length !== materielIds.length) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "存在无效的物料ID",
      });
    }

    if (warehouses.length !== warehouseIds.length) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "存在无效的仓库ID",
      });
    }

    // 事务处理：批量创建库存记录并更新物料总库存
    const result = await prisma.$transaction(async (tx) => {
      const createdStocks = [];
      const materielStockUpdates = new Map<number, number>();

      for (const item of stockData) {
        const {
          materiel_id,
          warehouse_id,
          batch_no,
          quantity,
          production_date,
          expiry_date,
          location,
          note,
        } = item;

        // 检查是否已存在相同的库存记录
        const existingStock = await tx.stock.findUnique({
          where: {
            materiel_id_warehouse_id_batch_no: {
              materiel_id,
              warehouse_id,
              batch_no,
            },
          },
        });

        if (existingStock) {
          // 如果已存在，则累加数量
          const updatedStock = await tx.stock.update({
            where: { id: existingStock.id },
            data: {
              quantity: { increment: quantity },
              production_date: production_date
                ? new Date(production_date)
                : undefined,
              expiry_date: expiry_date ? new Date(expiry_date) : undefined,
              location: location || undefined,
              note: note || undefined,
            },
          });
          createdStocks.push(updatedStock);
        } else {
          // 创建新的库存记录
          const newStock = await tx.stock.create({
            data: {
              materiel_id,
              warehouse_id,
              batch_no,
              quantity,
              production_date: production_date
                ? new Date(production_date)
                : undefined,
              expiry_date: expiry_date ? new Date(expiry_date) : undefined,
              location: location || undefined,
              note: note || undefined,
            },
          });
          createdStocks.push(newStock);
        }

        // 累计物料库存更新
        const currentUpdate = materielStockUpdates.get(materiel_id) || 0;
        materielStockUpdates.set(materiel_id, currentUpdate + quantity);
      }

      // 批量更新物料总库存
      for (const [materielId, totalQuantity] of materielStockUpdates) {
        await tx.materiel.update({
          where: { id: materielId },
          data: {
            stock: { increment: totalQuantity },
          },
        });
      }

      return createdStocks;
    });

    return {
      code: 1,
      message: `初始库存录入成功，共处理 ${result.length} 条记录`,
      data: result,
    };
  });

export default mergeRouters(
  router({
    queryStock,
    queryMaterielBatches,
    queryStockGrouped,
    stockIn,
    stockOut,
    stockTransfer,
    salesOut,
    querySalesOutRecords,
    createInitialStock,
  })
);
